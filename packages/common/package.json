{"name": "@libra/common", "version": "1.0.0", "private": true, "description": "Common utilities and types for Libra", "exports": {".": "./src/index.ts"}, "typesVersions": {"*": {"*": ["src/*"]}}, "scripts": {"build": "tsup", "clean": "rm -rf dist .turbo node_modules", "format-and-lint": "biome check .", "format-and-lint:fix": "biome check . --write", "typecheck": "tsc --noEmit", "update": "bun update"}, "dependencies": {"resend": "^4.7.0", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@trpc/tanstack-react-query": "^11.4.3"}, "devDependencies": {"@libra/typescript-config": "*"}}