{"name": "@libra/better-auth-stripe", "version": "1.0.0", "main": "./src/index.ts", "private": true, "description": "Stripe plugin for Better Auth", "keywords": ["stripe", "authentication", "better-auth", "plugin"], "scripts": {"build": "tsup", "clean": "rm -rf dist .turbo node_modules", "test": "vitest", "typecheck": "tsc --noEmit", "update": "bun update"}, "exports": {".": {"types": "./src/index.ts", "import": "./src/index.ts", "require": "./src/index.ts"}, "./client": {"types": "./src/client.ts", "import": "./src/client.ts", "require": "./src/client.ts"}}, "typesVersions": {"*": {"*": ["./src/index.ts"], "client": ["./src/client.ts"]}}, "dependencies": {"better-auth": "^1.3.4"}, "devDependencies": {"@libra/typescript-config": "*", "better-call": "^1.0.12"}}